"use client";
import Button from "@/components/common/Button";
import Tabs from "@/components/common/Tabs";
import SettingLayout from "@/layout/dashboard/SettingLayout";
import {
  createMultipleClients,
  eventCalendarSync,
  getCalendarEventClients,
  updateSyncStatus,
  useGetCalendarEvents,
} from "@/services/setting.service";
import { formatDate, formatTime } from "@/utils/axios";
import { Clock, EnvelopeSimple, Repeat } from "@phosphor-icons/react";
import moment from "moment";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import toast from "react-hot-toast";
import Modal from "@/components/common/Modal";
import THeader from "@/components/dashboard/common/table/THeader";
import CalendarTBody from "@/components/dashboard/common/table/CalendarTBody";
import SyncConfirmationModal from "@/components/common/SyncConfirmationModal";
import ConflictModal from "@/components/common/ConflictModal";
import RescheduledModal from "@/components/common/RescheduledModal";
import { AxiosError } from "axios";

const CalendarTab = [
  { label: "Syncable Events", value: "Syncable Events" },
  { label: "Non Syncable Events", value: "Non Syncable Events" },
];

const CalendarTableHeader = [
  "S.no",
  "Session",
  "Email",
  "Client Name ",
  "Mobile Number",
  "Amount",
];

interface emails {
  email?: string;
  organizer?: boolean;
  self?: boolean;
}

interface Item {
  kind?: string;
  summary?: string;
  start?: {
    dateTime: string;
    date: string;
    timeZone: string;
  };
  originalStartTime: {
    dateTime: string;
  };
  creator: {
    email: string;
  };
  email: string;
  description: string;
  status: string;
  depression: string;
  eventIds?: string[] | undefined;
  id?: string;
  totalRecurrenceData?: number;
  firstRecurrenceData?: {
    status?: string;
    fromDate?: string;
  };
  attendees?: emails[];
  isRescheduled?: boolean;
}

interface TableRow {
  isPresent: boolean;
  session: string;
  email: string;
  clientName: string;
  mobile: number;
  amount: number;
}





interface EventData {
  calendarEventId: string;
  exists: boolean;
  summary: string;
  attendee: string;
  client?: {
    name: string;
    mobileNumber: string;
    Amount: number;
  };
}

const CalendarSetting = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(CalendarTab[0]);
  const [loading, setLoading] = useState(false);
  const [selectedEventIds, setSelectedEventIds] = useState<string[]>([]);
  const [tableData, setTableData] = useState<TableRow[]>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [updateDisabled, setUpdateDisabled] = useState(false);
  const [syncDisabled, setSyncDisabled] = useState(true);
  const [isUpdateDisabled, setIsUpdateDisabled] = useState(false);
  const { resyncCalendarData, resyncCalendarLoading } = useGetCalendarEvents();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [isConflictModalOpen, setIsConflictModalOpen] = useState(false);
  const [conflictData, setConflictData] = useState<{
    message: string;
    conflicts: { startTime: string; endTime: string; clientName?: string; clientId?: string }[];
  }>({
    message: "",
    conflicts: [],
  });
  const [isRescheduledModalOpen, setIsRescheduledModalOpen] = useState(false);
  const [isSyncAllChecked, setIsSyncAllChecked] = useState(false);

  // Auto-manage sync all checkbox state
  useEffect(() => {
    if (resyncCalendarData?.syncable) {
      const confirmedEvents = resyncCalendarData.syncable.filter((event: Item) => !event.isRescheduled);
      const confirmedEventIds = confirmedEvents
        .map((event: Item) => event.id)
        .filter((id: string | undefined) => id !== undefined) as string[];

      // Check if all confirmed events are selected
      const allConfirmedSelected = confirmedEventIds.length > 0 &&
        confirmedEventIds.every(id => selectedEventIds.includes(id));

      setIsSyncAllChecked(allConfirmedSelected);
    }
  }, [selectedEventIds, resyncCalendarData?.syncable]);

  const handleContinueClick = async () => {
    setUpdateDisabled(false);
    setSyncDisabled(true);
    setIsUpdateDisabled(false); // Reset this to allow editing in the modal

    try {
      const selectedEvents = resyncCalendarData?.syncable?.filter(
        (event: Item) => event.id && selectedEventIds.includes(event.id)
      );

      if (!selectedEvents?.length) {
        console.warn("No selected events found.");
        return;
      }

      // Check if any selected events are rescheduled
      const rescheduledEvents = selectedEvents.filter((event: Item) => event.isRescheduled);

      // If there are rescheduled events, show the rescheduled modal
      if (rescheduledEvents.length > 0) {
        setIsRescheduledModalOpen(true);
        return;
      }

      console.log("Selected events for modal:", selectedEvents);
      const result = await getCalendarEventClients(selectedEvents);
      console.log("Calendar event clients result:", result);

      setIsModalOpen(true);

      // Prepare table data with only selected events
      const newTableData = result.events
        .filter((event: EventData) =>
          selectedEventIds.includes(event.calendarEventId)
        )
        .map((event: EventData) => ({
          isPresent: event.exists,
          session: event.summary,
          email: event.attendee,
          clientName: event.exists && event.client ? event.client.name : "",
          mobile:
            event.exists && event.client
              ? Number(event.client.mobileNumber) || ""
              : "",
          amount:
            event.exists && event.client
              ? Number(event.client.Amount) || ""
              : "",
        }));

      console.log("Prepared table data:", newTableData);
      setTableData(newTableData);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  // Validate form before updating
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    tableData.forEach((row, index) => {
      if (!row.clientName.trim()) {
        newErrors[`${index}-clientName`] = "Name is required*";
      }
      if (!row.amount) {
        newErrors[`${index}-amount`] = "Amount is required*";
      }

      const mobileStr = row.mobile?.toString().trim();
      if (mobileStr && !/^\d{10}$/.test(mobileStr)) {
        newErrors[`${index}-mobile`] =
          "Mobile number must be exactly 10 digits*";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdate = async () => {
    console.log("handleUpdate called");
    console.log("tableData:", tableData);

    if (!validateForm()) {
      console.log("Form validation failed");
      return;
    }

    try {
      const clients = tableData.map((row) => ({
        name: row.clientName,
        defaultSessionAmount: row.amount.toString(),
        email: row.email,
        phone: row.mobile.toString(),
      }));

      console.log("Creating clients:", clients);
      await createMultipleClients(clients);

      setUpdateDisabled(true);
      setIsUpdateDisabled(true);
      console.log("Update completed successfully");
    } catch (error) {
      console.error("Update failed", error);
      toast.error("Update failed. Please try again.");
    }
  };

  const handleRemoveSelectedEvents = () => {
    setSelectedEventIds([]);
  };

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    item: Item
  ) => {
    if (event.target.checked) {
      // Check if we're selecting a rescheduled event
      if (item.isRescheduled) {
        // If selecting rescheduled, clear any confirmed events
        const currentRescheduledEvents = resyncCalendarData?.syncable?.filter(
          (evt: Item) => evt.isRescheduled && selectedEventIds.includes(evt.id || "")
        ) || [];

        let newEventIds = [];
        if (item?.eventIds && item?.eventIds.length > 0) {
          newEventIds = [...item?.eventIds];
        } else {
          newEventIds.push(item.id);
        }

        // Only keep other rescheduled events and add this one
        const otherRescheduledIds = currentRescheduledEvents
          .flatMap((evt: Item) => evt.eventIds || [evt.id])
          .filter((id: string | undefined) => id !== undefined) as string[];

        setSelectedEventIds([
          ...otherRescheduledIds,
          ...(newEventIds.filter((id) => id !== undefined) as string[]),
        ]);
      } else {
        // If selecting confirmed, clear any rescheduled events
        const currentConfirmedEvents = resyncCalendarData?.syncable?.filter(
          (evt: Item) => !evt.isRescheduled && selectedEventIds.includes(evt.id || "")
        ) || [];

        let newEventIds = [];
        if (item?.eventIds && item?.eventIds.length > 0) {
          newEventIds = [...item?.eventIds];
        } else {
          newEventIds.push(item.id);
        }

        // Only keep other confirmed events and add this one
        const otherConfirmedIds = currentConfirmedEvents
          .flatMap((evt: Item) => evt.eventIds || [evt.id])
          .filter((id: string | undefined) => id !== undefined) as string[];

        setSelectedEventIds([
          ...otherConfirmedIds,
          ...(newEventIds.filter((id) => id !== undefined) as string[]),
        ]);
      }
    } else {
      let nIds = [];
      if (item?.eventIds && item?.eventIds.length > 0) {
        nIds = selectedEventIds.filter(
          (selectedId) => !item?.eventIds?.includes(selectedId)
        );
      } else {
        nIds = selectedEventIds.filter((selectedId) => selectedId !== item?.id);
      }
      setSelectedEventIds(nIds);
    }
  };

  async function updateSyncEventStatus() {
    await updateSyncStatus().catch((err) => {
      console.log(err);
    });
  }

  const syncCalendarEvents = async () => {
    setLoading(true);

    if (selectedEventIds.length <= 0) {
      toast.error("Please select at least one session");
      setLoading(false);
      return false;
    }

    try {
      const res = await eventCalendarSync({ eventIds: selectedEventIds });

      if (res.status === 200) {
        // Check if there are conflicts in the response
        if (res.data && res.data.success === false && res.data.conflicts) {
          // Handle conflicts
          setConflictData({
            message: res.data.message || "Conflicts found with existing sessions.",
            conflicts: res.data.conflicts || []
          });
          setIsConfirmationModalOpen(false);
          setTimeout(() => {
            setIsConflictModalOpen(true);
          }, 100);
          return false; // Return false to indicate conflicts
        }

        // No conflicts, sync was successful
        return true; // Return true to indicate success
      }
    } catch (err: unknown) {
      const axiosError = err as AxiosError;
      console.error(axiosError);

      // Check if the error response contains conflict data
      if (axiosError.response && axiosError.response.data &&
          typeof axiosError.response.data === 'object' &&
          'success' in axiosError.response.data &&
          axiosError.response.data.success === false &&
          'conflicts' in axiosError.response.data) {
        const errorData = axiosError.response.data as {
          message?: string;
          conflicts?: Array<{ startTime: string; endTime: string; clientName?: string; clientId?: string }>
        };
        setConflictData({
          message: errorData.message || "Conflicts found with existing sessions.",
          conflicts: errorData.conflicts || []
        });
        setIsConfirmationModalOpen(false);
        setTimeout(() => {
          setIsConflictModalOpen(true);
        }, 100);
        return false; // Return false to indicate conflicts
      } else {
        toast.error("Sync failed. Please try again.");
      }
    } finally {
      setLoading(false);
    }

    return false; // Return false by default for error cases
  };

  const handleSync = async () => {
    try {
      if (selectedEventIds.length <= 0) {
        toast.error("Please select at least one session", {
          position: "top-center",
        });
        return;
      }

      // Check if selected events are rescheduled
      const selectedEvents = resyncCalendarData?.syncable?.filter(
        (event: Item) => event.id && selectedEventIds.includes(event.id)
      );
      const hasRescheduledInSelection = selectedEvents?.some((event: Item) => event.isRescheduled);

      // Close the details modal first
      setIsModalOpen(false);

      if (hasRescheduledInSelection) {
        // Show rescheduled modal for rescheduled events
        setTimeout(() => {
          setIsRescheduledModalOpen(true);
        }, 100);
      } else {
        // For confirmed events, sync directly without confirmation modal
        setTimeout(async () => {
          try {
            setLoading(true);
            const syncResult = await syncCalendarEvents();

            if (syncResult) {
              toast.success("Calendar events synced successfully");
              await updateSyncEventStatus();
              router.push("/session");
            }
          } catch (error) {
            console.error("Sync failed", error);
            toast.error("Sync failed. Please try again.");
          } finally {
            setLoading(false);
            handleRemoveSelectedEvents();
          }
        }, 100);
      }
    } catch (error) {
      console.error("Sync failed", error);
      toast.error("Sync failed. Please try again.");
    }
  };

  // This function will be called after user confirms in the confirmation modal
  const handleConfirmSync = async () => {
    try {
      setLoading(true);

      // Proceed with sync directly
      const syncResult = await syncCalendarEvents();

      // Only close modals if sync was successful
      if (syncResult) {
        setIsModalOpen(false);
        setIsConfirmationModalOpen(false);
        toast.success("Calendar events synced successfully");
        await updateSyncEventStatus();
        router.push("/session");
      }
    } catch (error) {
      console.error("Sync failed", error);
      toast.error("Sync failed. Please try again.");
      setIsConfirmationModalOpen(false);
    } finally {
      setLoading(false);
      handleRemoveSelectedEvents();
      setIsModalOpen(false);
    }
  };

  // Handler for rescheduled modal confirmation
  const handleRescheduledConfirm = () => {
    setIsRescheduledModalOpen(false);
    handleRemoveSelectedEvents();
  };

  // Handler for closing the main modal and resetting states
  const handleModalClose = () => {
    setIsModalOpen(false);
    setUpdateDisabled(false);
    setIsUpdateDisabled(false);
    setErrors({});
  };

  return (
    <SettingLayout>
      <div>
        <h1 className="text-xl_30 font-semibold text-primary">
          Calendar Setting
        </h1>
        <div className="pt-6">
          <div className="flex justify-between">
            <Tabs
              tabs={CalendarTab}
              activeTab={activeTab?.label}
              setActiveTab={setActiveTab}
              handleRemoveSelectedEvents={handleRemoveSelectedEvents}
              sessionCount={
                activeTab?.label === "Syncable Events"
                  ? resyncCalendarData?.syncable.length || 0
                  : resyncCalendarData?.not_syncable?.length || 0
              }
            />
            {activeTab?.label === "Syncable Events" &&
              resyncCalendarData?.syncable.length > 0 && (
                <div className="flex items-center justify-between p-5">
                  <label htmlFor="syncAll" className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="syncAll"
                      className="w-4 h-4 accent-green-600 cursor-pointer"
                      checked={isSyncAllChecked}
                      onChange={(e) => {
                        if (e.target.checked) {
                          // Select only confirmed events (exclude rescheduled)
                          const confirmedEventIds = resyncCalendarData?.syncable
                            .filter((event: Item) => !event.isRescheduled)
                            .map((event: Item) => event.id)
                            .filter(
                              (id: string | undefined) => id !== undefined
                            ) as string[];
                          setSelectedEventIds(confirmedEventIds);
                        } else {
                          // Deselect all
                          setSelectedEventIds([]);
                        }
                      }}
                    />

                    <p className="text-base/6 text-green-600 font-semibold capitalize">
                      Sync All
                    </p>
                  </label>
                </div>
              )}
          </div>
          {resyncCalendarLoading ? ( // Check if loading
            <div className="grid sm:grid-cols-2 gap-4.5 py-7.5">
              {/* Display skeletons for loading state */}
              {Array.from({ length: 4 }).map((_, index) => (
                <div
                  key={index}
                  className="border border-green-600/20 rounded-base overflow-hidden animate-pulse"
                >
                  <div className="flex items-center justify-between p-5 bg-yellow-100">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
                      <div className="h-6 w-1/3 bg-gray-200 rounded-full"></div>
                    </div>
                    <div className="h-6 w-1/4 bg-gray-200 rounded-full"></div>
                  </div>
                  <div className="p-5">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <div className="h-4 w-1/2 bg-gray-200 rounded-full"></div>
                        <div className="h-4 w-2/3 bg-gray-200 rounded-full"></div>
                      </div>
                      <div className="relative group">
                        <div className="h-4 w-1/4 bg-gray-200 rounded-full"></div>
                      </div>
                    </div>
                    <hr className="my-5" />
                    <div className="h-4 w-1/4 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <>
              {activeTab?.label === "Syncable Events" && (
                <div>
                   {/* Explanatory note for syncable events */}
                  <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-sm text-gray-800">
                      Hey! We noticed a few sessions - some new, some old that are showing up on your calendar but haven&apos;t been synced yet. Just a quick nudge to sync them so we don&apos;t miss anything important.
                    </p>
                  </div>
                  <div className="grid sm:grid-cols-2 gap-4.5 py-7.5">
                    {resyncCalendarData?.syncable?.length > 0 &&
                      resyncCalendarData?.syncable?.map(
                        (item: Item, index: number) => {
                          const attendees =
                            item.attendees &&
                            item.attendees.filter(
                              (attend) => attend.self != true
                            );
                          return (
                            <div
                              key={index}
                              className={`border rounded-base overflow-hidden ${
                                item.isRescheduled
                                  ? "border-orange-400/40 bg-orange-50/30"
                                  : "border-green-600/20"
                              }`}
                            >
                              <div className={`flex items-center justify-between p-5 ${
                                item.isRescheduled
                                  ? "bg-orange-100"
                                  : "bg-yellow-100"
                              }`}>
                                <label className="flex items-center gap-3">
                                  <input
                                    type="checkbox"
                                    className="w-4 h-4 accent-green-600 cursor-pointer"
                                    checked={
                                      item.id
                                        ? selectedEventIds.includes(item.id)
                                        : false
                                    }
                                    onChange={(event) =>
                                      handleChange(event, item)
                                    }
                                  />
                                  <p className="text-base/6 text-green-600 font-semibold capitalize">
                                    {item?.summary}
                                  </p>
                                </label>
                                <p className="text-base/6 text-green-600/70 font-semibold">
                                  Session :{" "}
                                  {index > 9 ? index + 1 : `0${index + 1}`}
                                </p>
                              </div>
                              <div className="p-5">
                                <div className="flex justify-between items-start">
                                  <div className="space-y-2">
                                    <p className="flex items-center gap-2 text-base/6 text-primary/90 font-semibold">
                                      <Clock
                                        size={24}
                                        className="text-[#44B7E5]"
                                      />{" "}
                                      {item.start?.timeZone ===
                                      "Asia/Kolkata" ? (
                                        <>
                                          {item.start?.dateTime ? (
                                            <>
                                              {moment(
                                                item.start?.dateTime
                                              ).format("YYYY-MM-DD")}{" "}
                                              | {/* Only Date */}
                                              {moment(
                                                item.start?.dateTime
                                              ).format("hh:mm A")}{" "}
                                              {/* Only Time (HH:MM) */}
                                            </>
                                          ) : (
                                            <>
                                              {moment(item.start?.date).format(
                                                "hh:mm"
                                              )}{" "}
                                              {/* Only Time (HH:MM) */}
                                            </>
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          {item.start?.dateTime ? (
                                            <>
                                              {formatDate(
                                                item.start?.dateTime || ""
                                              )}{" "}
                                              |{" "}
                                              {formatTime(
                                                item.start?.dateTime || ""
                                              )}
                                            </>
                                          ) : (
                                            <>
                                              {formatDate(
                                                item.start?.date || ""
                                              )}
                                            </>
                                          )}
                                        </>
                                      )}
                                    </p>
                                    <p className="flex items-center gap-2 text-base/6 text-primary/90">
                                      <EnvelopeSimple
                                        size={24}
                                        className="text-[#F6A002]"
                                      />
                                      {attendees &&
                                        attendees.length > 0 &&
                                        attendees[0].email}
                                    </p>
                                  </div>
                                  <div className="relative group">
                                    <p className={`text-sm/5 px-3 py-1.5 rounded-full inline-block capitalize ${
                                      item.isRescheduled
                                        ? "text-orange-600 bg-orange-200"
                                        : "text-green-500 bg-green-200"
                                    }`}>
                                      {item.isRescheduled ? "Rescheduled" : item?.status} !
                                    </p>
                                  </div>
                                </div>
                                <hr className="my-5" />
                                <p className="text-base/6 text-primary/90 font-medium flex items-center gap-2">
                                  <Repeat size={22} /> Frequency: +
                                  {item?.eventIds?.length}
                                </p>
                              </div>
                            </div>
                          );
                        }
                      )}
                  </div>
                  {resyncCalendarData?.syncable?.length === 0 && (
                    <div className="p-15px text-center text-primary/70 text-sm/5">
                      <div className="flex flex-col items-center justify-center pt-5">
                        <Image
                          width={1000}
                          height={1000}
                          src="/assets/images/dashboard/not-found-session-list.webp"
                          alt="no-data"
                          className="w-[185px] h-auto"
                        />
                        <p className="text-xl/6 font-bold pt-4 text-primary">
                          No Events Found!
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
              {activeTab?.label === "Non Syncable Events" && (
                <div>
                  {/* Explanatory note for non-syncable events */}
                  <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-sm text-gray-800">
                      Oops! We couldn&apos;t find any guest in these sessions so we&apos;re assuming these are personal blocks on your calendar.
                    </p>
                  </div>
                  <div className="grid sm:grid-cols-2 gap-4.5 py-7.5">
                    {resyncCalendarData?.not_syncable?.map(
                      (item: Item, index: number) => {
                        const attendees =
                          item.attendees &&
                          item.attendees.filter(
                            (attend) => attend.self != true
                          );
                        return (
                          <div
                            key={index}
                            className="border border-green-600/20 rounded-base overflow-hidden "
                          >
                            <div className="flex items-center justify-between p-5 bg-yellow-100 ">
                              <label className="flex items-center gap-3">
                                {/* <input
                                type="checkbox"
                                className="w-4 h-4 accent-green-600 cursor-pointer"
                              /> */}
                                <p className="text-base/6 text-green-600 font-semibold capitalize">
                                  {item?.summary}
                                </p>
                              </label>
                              <p className="text-base/6 text-green-600/70 font-semibold">
                                Session :{" "}
                                {index > 9 ? index + 1 : `0${index + 1}`}
                              </p>
                            </div>
                            <div className="p-5">
                              <div className="flex justify-between items-start">
                                <div className="space-y-2">
                                  <p className="flex items-center gap-2 text-base/6 text-primary/90 font-semibold">
                                    <Clock
                                      size={24}
                                      className="text-[#44B7E5]"
                                    />{" "}
                                    {item.start?.timeZone === "Asia/Kolkata" ? (
                                      <>
                                        {item.start?.dateTime ? (
                                          <>
                                            {moment(
                                              item.start?.dateTime
                                            ).format("YYYY-MM-DD")}{" "}
                                            | {/* Only Date */}
                                            {moment(
                                              item.start?.dateTime
                                            ).format("hh:mm A")}{" "}
                                            {/* Only Time (HH:MM) */}
                                          </>
                                        ) : (
                                          <>
                                            {moment(item.start?.date).format(
                                              "hh:mm"
                                            )}{" "}
                                            {/* Only Time (HH:MM) */}
                                          </>
                                        )}
                                      </>
                                    ) : (
                                      <>
                                        {item.start?.dateTime ? (
                                          <>
                                            {formatDate(
                                              item.start?.dateTime || ""
                                            )}{" "}
                                            |{" "}
                                            {formatTime(
                                              item.start?.dateTime || ""
                                            )}
                                          </>
                                        ) : (
                                          <>
                                            {formatDate(item.start?.date || "")}
                                          </>
                                        )}
                                      </>
                                    )}
                                  </p>
                                  {attendees &&
                                    attendees.length > 0 &&
                                    attendees[0].email && (
                                      <p className="flex items-center gap-2 text-base/6 text-primary/90">
                                        <EnvelopeSimple
                                          size={24}
                                          className="text-[#F6A002]"
                                        />
                                        {attendees &&
                                          attendees.length > 0 &&
                                          attendees[0].email}
                                      </p>
                                    )}
                                </div>
                                <div className="relative group">
                                  <p className="text-sm/5 text-green-500 px-3 py-1.5 rounded-full inline-block bg-green-200">
                                    {item?.status} !
                                  </p>
                                  <div className="absolute right-0 top-4 p-2.5 bg-white w-[202px] rounded-base shadow-[0px_4px_20.9px_0px_#0000001A] invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-300">
                                    <p className="text-xs_18 text-primary">
                                      This events does not have any{" "}
                                      <span className="font-semibold">
                                        attendees email
                                      </span>
                                      , hence this can not be synced!
                                    </p>
                                  </div>
                                </div>
                              </div>
                              <hr className="my-5" />
                              <p className="text-base/6 text-primary/90 font-medium flex items-center gap-2">
                                <Repeat size={22} /> Frequency: +
                                {item?.eventIds?.length}
                              </p>
                            </div>
                          </div>
                        );
                      }
                    )}
                  </div>
                  {resyncCalendarData?.not_syncable?.length === 0 && (
                    <div className="p-15px text-center text-primary/70 text-sm/5">
                      <div className="flex flex-col items-center justify-center pt-5">
                        <Image
                          width={1000}
                          height={1000}
                          src="/assets/images/dashboard/not-found-session-list.webp"
                          alt="no-data"
                          className="w-[185px] h-auto"
                        />
                        <p className="text-xl/6 font-bold pt-4 text-primary">
                          No Events Found!
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
        {activeTab?.value === "Syncable Events" &&
          resyncCalendarData?.syncable?.length > 0 &&
          selectedEventIds?.length > 0 && (
            <div className="sticky bottom-0 z-40 bg-white py-2.5 flex justify-end items-center">
              <Button
                variant="filled"
                className="mt-auto"
                // onClick={syncCalendarEvents}
                onClick={handleContinueClick}
                disabled={loading}
              >
                {loading ? "Loading..." : "Continue"}
              </Button>
            </div>
          )}

        {isModalOpen && (
          <Modal
            open={isModalOpen}
            handler={handleModalClose}
            size="xl"
            email={resyncCalendarData?.syncable[0]?.attendees[0]?.email}
          >
            <div className="p-5">
              <h2 className="text-xl font-semibold">Required Details*</h2>
              <div className="overflow-x-auto py-4">
                <div className="rounded-2xl overflow-hidden border border-gray-300">
                  <table className="w-full bg-white border-separate">
                    <THeader data={CalendarTableHeader} />
                    <CalendarTBody
                      TableData={{ data: tableData }}
                      handleUpdate={handleUpdate}
                      handleSync={handleSync}
                      errors={errors}
                      setErrors={setErrors}
                      setTableData={setTableData}
                      isUpdateDisabled={isUpdateDisabled}
                    />
                  </table>
                </div>
              </div>

              <div className="flex justify-center gap-3 mt-4">
              {(() => {
                const allFieldsFilled = tableData.every((row) => row.clientName && row.amount);
                const shouldShowSync = allFieldsFilled && isUpdateDisabled;
                console.log("Button logic:", { allFieldsFilled, isUpdateDisabled, shouldShowSync, updateDisabled });

                return shouldShowSync ? (
                  <Button
                    onClick={handleSync}
                    className="px-4 py-2 text-white rounded"
                    disabled={loading && syncDisabled}
                  >
                    {loading ? "Syncing..." : "Sync"}
                  </Button>
                ) : (
                  <Button
                    onClick={handleUpdate}
                    className="px-4 py-2 text-white rounded"
                    disabled={updateDisabled}
                  >
                    Update
                  </Button>
                );
              })()}
              </div>
            </div>
          </Modal>
        )}

        {/* Confirmation Modal */}
        <SyncConfirmationModal
          open={isConfirmationModalOpen}
          onClose={() => setIsConfirmationModalOpen(false)}
          onConfirm={handleConfirmSync}
          loading={loading}
        />

        {/* Rescheduled Modal */}
        <RescheduledModal
          open={isRescheduledModalOpen}
          onClose={() => setIsRescheduledModalOpen(false)}
          onConfirm={handleRescheduledConfirm}
          loading={loading}
        />

        {/* Conflict Modal */}
        <ConflictModal
          open={isConflictModalOpen}
          onClose={() => {
            setIsConflictModalOpen(false);
            setIsConfirmationModalOpen(false);
            // Clear conflict data when closing
            setConflictData({
              message: "",
              conflicts: []
            });
          }}
          message={conflictData.message}
          conflicts={conflictData.conflicts}
          isCalendarSync={true}
        />
      </div>
    </SettingLayout>
  );
};

export default CalendarSetting;
